/**
 * WebSocket Manager for Valorant Strategy Board
 * Handles server-based connections and real-time synchronization
 * Replaces WebRTC for better browser compatibility
 */

class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.isHost = false;
        this.sessionCode = null;
        this.sessionName = '';
        this.localUserId = this.generateUserId();
        this.localUserName = 'User_' + this.localUserId.substring(0, 6);
        this.connectedUsers = new Map();
        
        // Server URL - Update this to your deployed server URL
        this.serverUrl = this.getServerUrl();
        
        // Event callbacks (same interface as WebRTC manager)
        this.onConnectionEstablished = null;
        this.onConnectionLost = null;
        this.onDataReceived = null;
        this.onUserJoined = null;
        this.onUserLeft = null;
        this.onError = null;
        
        console.log('WebSocket Manager initialized with user ID:', this.localUserId);
        console.log('Server URL:', this.serverUrl);
    }
    
    /**
     * Get server URL based on environment
     */
    getServerUrl() {
        // Check if we're in development or production
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            // Development - assume server is running locally
            return 'http://localhost:8000';
        } else {
            // Production - use your deployed server URL
            // Replace this with your actual Render.com URL
            // You can also use environment variables or config files
            return 'https://valo-strat-board-server.onrender.com';
        }
    }
    
    /**
     * Generate a unique user ID
     */
    generateUserId() {
        return 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    }
    
    /**
     * Generate a 6-character session code
     */
    generateSessionCode() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        let code = '';
        for (let i = 0; i < 6; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return code;
    }
    
    /**
     * Connect to the server
     */
    async connectToServer() {
        return new Promise((resolve, reject) => {
            try {
                // Import Socket.IO client library
                if (typeof io === 'undefined') {
                    throw new Error('Socket.IO client library not loaded');
                }
                
                this.socket = io(this.serverUrl, {
                    transports: ['websocket', 'polling'],
                    timeout: 10000,
                    forceNew: true
                });
                
                this.socket.on('connect', () => {
                    console.log('Connected to server');
                    this.isConnected = true;
                    this.setupEventHandlers();
                    resolve();
                });
                
                this.socket.on('connect_error', (error) => {
                    console.error('Connection error:', error);
                    this.isConnected = false;
                    if (this.onError) this.onError(error);
                    reject(error);
                });
                
                this.socket.on('disconnect', (reason) => {
                    console.log('Disconnected from server:', reason);
                    this.isConnected = false;
                    if (this.onConnectionLost) this.onConnectionLost('server');
                });
                
            } catch (error) {
                console.error('Failed to connect to server:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Set up event handlers for server communication
     */
    setupEventHandlers() {
        this.socket.on('session_created', (data) => {
            console.log('Session created:', data);
            this.sessionCode = data.session_code;
            this.sessionName = data.session_name;
            this.isHost = data.is_host;
            
            if (this.onConnectionEstablished) {
                this.onConnectionEstablished();
            }
        });
        
        this.socket.on('session_joined', (data) => {
            console.log('Session joined:', data);
            this.sessionCode = data.session_code;
            this.sessionName = data.session_name;
            this.isHost = data.is_host;
            
            // Add existing users
            if (data.connected_users) {
                data.connected_users.forEach(user => {
                    if (user.user_id !== this.localUserId) {
                        this.connectedUsers.set(user.user_id, user);
                    }
                });
            }
            
            if (this.onConnectionEstablished) {
                this.onConnectionEstablished();
            }
        });
        
        this.socket.on('user_joined', (data) => {
            console.log('User joined:', data);
            this.connectedUsers.set(data.user_id, data);
            
            if (this.onUserJoined) {
                this.onUserJoined(data.user_id, data.user_name);
            }
        });
        
        this.socket.on('user_left', (data) => {
            console.log('User left:', data);
            this.connectedUsers.delete(data.user_id);
            
            if (this.onUserLeft) {
                this.onUserLeft(data.user_id);
            }
        });
        
        this.socket.on('canvas_change', (data) => {
            console.log('Canvas change received:', data);
            
            if (this.onDataReceived) {
                this.onDataReceived(data, data.from_user_id);
            }
        });
        
        this.socket.on('cursor_update', (data) => {
            // Handle cursor updates if needed
            if (this.onDataReceived) {
                this.onDataReceived({
                    type: 'cursor-update',
                    ...data
                }, data.user_id);
            }
        });
        
        this.socket.on('error', (data) => {
            console.error('Server error:', data);
            if (this.onError) {
                this.onError(new Error(data.message));
            }
        });
    }
    
    /**
     * Create a new session (host)
     */
    async createSession(sessionName = 'Strategy Session') {
        try {
            if (!this.isConnected) {
                await this.connectToServer();
            }
            
            this.sessionName = sessionName;
            
            return new Promise((resolve, reject) => {
                this.socket.emit('create_session', {
                    session_name: sessionName,
                    user_name: this.localUserName,
                    user_id: this.localUserId
                });
                
                // Wait for session_created event
                const timeout = setTimeout(() => {
                    reject(new Error('Session creation timeout'));
                }, 10000);
                
                const onSessionCreated = (data) => {
                    clearTimeout(timeout);
                    this.socket.off('session_created', onSessionCreated);
                    resolve(data.session_code);
                };
                
                this.socket.on('session_created', onSessionCreated);
            });
            
        } catch (error) {
            console.error('Failed to create session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Join an existing session
     */
    async joinSession(sessionCode) {
        try {
            if (!this.isConnected) {
                await this.connectToServer();
            }
            
            this.sessionCode = sessionCode.toUpperCase();
            
            return new Promise((resolve, reject) => {
                this.socket.emit('join_session', {
                    session_code: this.sessionCode,
                    user_name: this.localUserName,
                    user_id: this.localUserId
                });
                
                // Wait for session_joined event
                const timeout = setTimeout(() => {
                    reject(new Error('Session join timeout'));
                }, 10000);
                
                const onSessionJoined = (data) => {
                    clearTimeout(timeout);
                    this.socket.off('session_joined', onSessionJoined);
                    resolve();
                };
                
                const onError = (error) => {
                    clearTimeout(timeout);
                    this.socket.off('error', onError);
                    reject(new Error(error.message));
                };
                
                this.socket.on('session_joined', onSessionJoined);
                this.socket.on('error', onError);
            });
            
        } catch (error) {
            console.error('Failed to join session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Broadcast data to all connected peers
     */
    broadcast(data) {
        if (!this.isConnected || !this.socket) {
            console.warn('Cannot broadcast: not connected to server');
            return 0;
        }
        
        try {
            this.socket.emit('canvas_change', data);
            console.log('Broadcasted data to server');
            return this.connectedUsers.size;
        } catch (error) {
            console.error('Error broadcasting data:', error);
            return 0;
        }
    }
    
    /**
     * Send cursor update
     */
    sendCursorUpdate(x, y) {
        if (!this.isConnected || !this.socket) {
            return;
        }
        
        try {
            this.socket.emit('cursor_update', {
                x: x,
                y: y
            });
        } catch (error) {
            console.error('Error sending cursor update:', error);
        }
    }
    
    /**
     * Get list of connected peer IDs
     */
    getConnectedPeers() {
        return Array.from(this.connectedUsers.keys());
    }
    
    /**
     * Check if connected to any peers
     */
    isConnected() {
        return this.isConnected && this.connectedUsers.size > 0;
    }
    
    /**
     * Disconnect from server and leave session
     */
    disconnect() {
        console.log('Disconnecting from server...');
        
        if (this.socket && this.isConnected) {
            this.socket.emit('leave_session', {
                session_code: this.sessionCode
            });
            
            this.socket.disconnect();
        }
        
        // Reset state
        this.socket = null;
        this.isConnected = false;
        this.isHost = false;
        this.sessionCode = null;
        this.sessionName = '';
        this.connectedUsers.clear();
        
        console.log('Disconnected successfully');
    }
    
    /**
     * Get session information
     */
    getSessionInfo() {
        return {
            sessionCode: this.sessionCode,
            sessionName: this.sessionName,
            isHost: this.isHost,
            connectedPeers: this.connectedUsers.size,
            localUserId: this.localUserId,
            localUserName: this.localUserName
        };
    }
}

// Export for use in other files
window.WebSocketManager = WebSocketManager;
