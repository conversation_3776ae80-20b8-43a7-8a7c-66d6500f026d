/**
 * WebSocket Manager for Valorant Strategy Board
 * Handles server-based connections and real-time synchronization
 * Replaces WebRTC for better browser compatibility
 */

class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.isHost = false;
        this.sessionCode = null;
        this.sessionName = '';
        this.localUserId = this.generateUserId();
        this.localUserName = 'User_' + this.localUserId.substring(0, 6);
        this.connectedUsers = new Map();
        
        // Server URL - Update this to your deployed server URL
        this.serverUrl = this.getServerUrl();
        
        // Event callbacks (same interface as WebRTC manager)
        this.onConnectionEstablished = null;
        this.onConnectionLost = null;
        this.onDataReceived = null;
        this.onUserJoined = null;
        this.onUserLeft = null;
        this.onError = null;
        
        console.log('WebSocket Manager initialized with user ID:', this.localUserId);
        console.log('Server URL:', this.serverUrl);
    }
    
    /**
     * Get server URL based on environment
     */
    getServerUrl() {
        // Check if we're in development or production
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            // Development - assume server is running locally
            return 'http://localhost:8000';
        } else {
            // Production - use your deployed server URL
            // Replace this with your actual Render.com URL
            // You can also use environment variables or config files
            return 'http://0.0.0.0:8000';
        }
    }
    
    /**
     * Generate a unique user ID
     */
    generateUserId() {
        return 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    }
    
    /**
     * Generate a 6-character session code
     */
    generateSessionCode() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        let code = '';
        for (let i = 0; i < 6; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return code;
    }
    
    /**
     * Connect to the server
     */
    async connectToServer() {
        return new Promise((resolve, reject) => {
            try {
                // Use native WebSocket instead of Socket.IO
                const wsUrl = this.serverUrl.replace('http://', 'ws://').replace('https://', 'wss://');
                this.socket = new WebSocket(wsUrl);

                this.socket.onopen = () => {
                    console.log('Connected to server');
                    this.isConnected = true;
                    this.setupEventHandlers();
                    resolve();
                };

                this.socket.onerror = (error) => {
                    console.error('Connection error:', error);
                    this.isConnected = false;
                    if (this.onError) this.onError(error);
                    reject(error);
                };

                this.socket.onclose = (event) => {
                    console.log('Disconnected from server:', event.reason);
                    this.isConnected = false;
                    if (this.onConnectionLost) this.onConnectionLost('server');
                };

            } catch (error) {
                console.error('Failed to connect to server:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Set up event handlers for server communication
     */
    setupEventHandlers() {
        this.socket.onmessage = (event) => {
            try {
                console.log('Received raw message:', event.data);
                const data = JSON.parse(event.data);
                console.log('Parsed message data:', data);
                this.handleServerMessage(data);
            } catch (error) {
                console.error('Error parsing server message:', error);
            }
        };
    }

    /**
     * Handle incoming server messages
     */
    handleServerMessage(data) {
        switch (data.type) {
            case 'connected':
                console.log('Connected to server with ID:', data.client_id);
                break;

            case 'session_created':
                console.log('Session created:', data);
                this.sessionCode = data.session_code;
                this.sessionName = data.session_name;
                this.isHost = data.is_host;

                // Resolve pending session creation
                if (this.pendingSessionCreation) {
                    this.pendingSessionCreation.resolve(data.session_code);
                    this.pendingSessionCreation = null;
                }

                if (this.onConnectionEstablished) {
                    this.onConnectionEstablished();
                }
                break;

            case 'session_joined':
                console.log('Session joined:', data);
                this.sessionCode = data.session_code;
                this.sessionName = data.session_name;
                this.isHost = data.is_host;

                // Add existing users
                if (data.connected_users) {
                    data.connected_users.forEach(user => {
                        if (user.user_id !== this.localUserId) {
                            this.connectedUsers.set(user.user_id, user);
                        }
                    });
                }

                // Resolve pending session join
                if (this.pendingSessionJoin) {
                    this.pendingSessionJoin.resolve();
                    this.pendingSessionJoin = null;
                }

                if (this.onConnectionEstablished) {
                    this.onConnectionEstablished();
                }
                break;

            case 'user_joined':
                console.log('User joined:', data);
                this.connectedUsers.set(data.user_id, data);

                if (this.onUserJoined) {
                    this.onUserJoined(data.user_id, data.user_name);
                }
                break;

            case 'user_left':
                console.log('User left:', data);
                this.connectedUsers.delete(data.user_id);

                if (this.onUserLeft) {
                    this.onUserLeft(data.user_id);
                }
                break;

            case 'canvas_change':
            case 'canvas-change':
                console.log('Canvas change received:', data);
                console.log('Calling onDataReceived with:', data, data.from_user_id);

                // Add visual debugging
                console.log('🎨 CANVAS CHANGE RECEIVED from:', data.from_user_name);

                // Create a temporary visual indicator
                const indicator = document.createElement('div');
                indicator.style.cssText = `
                    position: fixed; top: 10px; right: 10px;
                    background: #4CAF50; color: white;
                    padding: 10px; border-radius: 5px;
                    z-index: 10000; font-size: 14px;
                `;
                indicator.textContent = `📡 Received change from ${data.from_user_name}`;
                document.body.appendChild(indicator);
                setTimeout(() => indicator.remove(), 3000);

                if (this.onDataReceived) {
                    this.onDataReceived(data, data.from_user_id);
                } else {
                    console.warn('onDataReceived callback not set!');
                    alert('onDataReceived callback not set!');
                }
                break;

            case 'cursor_update':
                // Handle cursor updates if needed
                if (this.onDataReceived) {
                    this.onDataReceived({
                        type: 'cursor-update',
                        ...data
                    }, data.user_id);
                }
                break;

            case 'error':
                console.error('Server error:', data);

                // Reject pending operations
                if (this.pendingSessionCreation) {
                    this.pendingSessionCreation.reject(new Error(data.message));
                    this.pendingSessionCreation = null;
                }
                if (this.pendingSessionJoin) {
                    this.pendingSessionJoin.reject(new Error(data.message));
                    this.pendingSessionJoin = null;
                }

                if (this.onError) {
                    this.onError(new Error(data.message));
                }
                break;

            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    /**
     * Create a new session (host)
     */
    async createSession(sessionName = 'Strategy Session') {
        try {
            if (!this.isConnected) {
                await this.connectToServer();
            }

            this.sessionName = sessionName;

            return new Promise((resolve, reject) => {
                // Set up one-time listener for session_created
                this.pendingSessionCreation = { resolve, reject };

                // Send create session message
                const message = {
                    type: 'create_session',
                    session_name: sessionName,
                    user_name: this.localUserName,
                    user_id: this.localUserId
                };

                this.socket.send(JSON.stringify(message));

                // Set timeout
                setTimeout(() => {
                    if (this.pendingSessionCreation) {
                        this.pendingSessionCreation.reject(new Error('Session creation timeout'));
                        this.pendingSessionCreation = null;
                    }
                }, 10000);
            });

        } catch (error) {
            console.error('Failed to create session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Join an existing session
     */
    async joinSession(sessionCode) {
        try {
            if (!this.isConnected) {
                await this.connectToServer();
            }

            this.sessionCode = sessionCode.toUpperCase();

            return new Promise((resolve, reject) => {
                // Set up one-time listener for session_joined
                this.pendingSessionJoin = { resolve, reject };

                // Send join session message
                const message = {
                    type: 'join_session',
                    session_code: this.sessionCode,
                    user_name: this.localUserName,
                    user_id: this.localUserId
                };

                this.socket.send(JSON.stringify(message));

                // Set timeout
                setTimeout(() => {
                    if (this.pendingSessionJoin) {
                        this.pendingSessionJoin.reject(new Error('Session join timeout'));
                        this.pendingSessionJoin = null;
                    }
                }, 10000);
            });

        } catch (error) {
            console.error('Failed to join session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Broadcast data to all connected peers
     */
    broadcast(data) {
        if (!this.isConnected || !this.socket) {
            console.warn('Cannot broadcast: not connected to server');
            return 0;
        }

        try {
            const message = {
                type: 'canvas_change',
                ...data
            };
            this.socket.send(JSON.stringify(message));
            console.log('Broadcasted data to server');
            return this.connectedUsers.size;
        } catch (error) {
            console.error('Error broadcasting data:', error);
            return 0;
        }
    }

    /**
     * Send cursor update
     */
    sendCursorUpdate(x, y) {
        if (!this.isConnected || !this.socket) {
            return;
        }

        try {
            const message = {
                type: 'cursor_update',
                x: x,
                y: y
            };
            this.socket.send(JSON.stringify(message));
        } catch (error) {
            console.error('Error sending cursor update:', error);
        }
    }
    
    /**
     * Get list of connected peer IDs
     */
    getConnectedPeers() {
        return Array.from(this.connectedUsers.keys());
    }
    
    /**
     * Check if connected to any peers
     */
    isConnectedToPeers() {
        return this.isConnected && this.connectedUsers.size > 0;
    }
    
    /**
     * Disconnect from server and leave session
     */
    disconnect() {
        console.log('Disconnecting from server...');

        if (this.socket && this.isConnected) {
            // Send leave session message
            const message = {
                type: 'leave_session',
                session_code: this.sessionCode
            };
            this.socket.send(JSON.stringify(message));

            // Close WebSocket connection
            this.socket.close();
        }

        // Reset state
        this.socket = null;
        this.isConnected = false;
        this.isHost = false;
        this.sessionCode = null;
        this.sessionName = '';
        this.connectedUsers.clear();
        this.pendingSessionCreation = null;
        this.pendingSessionJoin = null;

        console.log('Disconnected successfully');
    }
    
    /**
     * Get session information
     */
    getSessionInfo() {
        return {
            sessionCode: this.sessionCode,
            sessionName: this.sessionName,
            isHost: this.isHost,
            connectedPeers: this.connectedUsers.size,
            localUserId: this.localUserId,
            localUserName: this.localUserName
        };
    }
}

// Export for use in other files
window.WebSocketManager = WebSocketManager;
