/**
 * Collaboration Manager for Valorant Strategy Board
 * Handles real-time synchronization of canvas changes and user interactions
 */

class CollaborationManager {
    constructor(canvas, webrtcManager) {
        this.canvas = canvas;
        this.webrtc = webrtcManager;
        this.connectedUsers = new Map();
        this.userCursors = new Map();
        this.isCollaborating = false;
        this.lastSyncTime = 0;
        this.syncThrottle = 100; // Minimum time between syncs in ms

        // Bind WebRTC event handlers
        this.setupWebRTCHandlers();

        // Bind canvas event handlers
        this.setupCanvasHandlers();

        console.log('Collaboration Manager initialized');
    }

    /**
     * Set up WebSocket event handlers
     */
    setupWebRTCHandlers() {
        this.webrtc.onConnectionEstablished = () => {
            console.log('Collaboration connection established');
            this.isCollaborating = true;
            this.updateConnectionStatus('connected');
            this.showUsersPanel();
        };

        this.webrtc.onConnectionLost = (peerId) => {
            console.log('Collaboration connection lost:', peerId);
            this.removeUser(peerId);
            if (this.webrtc.getConnectedPeers().length === 0) {
                this.isCollaborating = false;
                this.updateConnectionStatus('offline');
                this.hideUsersPanel();
            }
        };

        this.webrtc.onDataReceived = (data, fromPeerId) => {
            this.handleIncomingData(data, fromPeerId);
        };

        this.webrtc.onError = (error) => {
            console.error('WebSocket error:', error);
            this.updateConnectionStatus('error');
        };
    }

    /**
     * Set up canvas event handlers for collaboration
     */
    setupCanvasHandlers() {
        // Track object modifications
        this.canvas.on('object:added', (e) => {
            console.log('Object added event:', {
                isCollaborating: this.isCollaborating,
                isLoadingFromJSON: this.canvas.isLoadingFromJSON,
                isFromRemote: e.target.isFromRemote,
                objectType: e.target.type
            });

            if (this.isCollaborating && !this.canvas.isLoadingFromJSON && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:added', e.target);
                });
            }
        });

        this.canvas.on('object:modified', (e) => {
            if (this.isCollaborating && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:modified', e.target);
                });
            }
        });

        this.canvas.on('object:removed', (e) => {
            if (this.isCollaborating && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:removed', e.target);
                });
            }
        });

        this.canvas.on('path:created', (e) => {
            if (this.isCollaborating && e.path && !e.path.isFromRemote) {
                this.throttledSync(() => {
                    this.broadcastCanvasChange('path:created', e.path);
                });
            }
        });

        // Track mouse movements for cursor sharing
        this.canvas.on('mouse:move', (e) => {
            if (this.isCollaborating) {
                this.throttledCursorUpdate(e);
            }
        });
    }

    /**
     * Throttle function calls to prevent spam
     */
    throttledSync(callback) {
        const now = Date.now();
        if (now - this.lastSyncTime > this.syncThrottle) {
            this.lastSyncTime = now;
            callback();
        }
    }

    /**
     * Throttled cursor position updates
     */
    throttledCursorUpdate(e) {
        if (!this.lastCursorUpdate || Date.now() - this.lastCursorUpdate > 50) {
            this.lastCursorUpdate = Date.now();
            const pointer = this.canvas.getPointer(e.e);
            this.broadcastCursorPosition(pointer.x, pointer.y);
        }
    }

    /**
     * Broadcast canvas changes to all connected peers
     */
    broadcastCanvasChange(eventType, object) {
        try {
            console.log('Broadcasting canvas change:', eventType, 'Object:', object);
            const objectData = this.serializeObject(object);
            console.log('Serialized object data:', objectData);

            const changeData = {
                type: 'canvas-change',
                eventType: eventType,
                objectData: objectData,
                timestamp: Date.now(),
                userId: this.webrtc.localUserId
            };

            console.log('Sending change data:', changeData);
            this.webrtc.broadcast(changeData);
            console.log('Broadcasted canvas change:', eventType);
        } catch (error) {
            console.error('Error broadcasting canvas change:', error);
        }
    }

    /**
     * Broadcast cursor position to all connected peers
     */
    broadcastCursorPosition(x, y) {
        // Use WebSocket manager's sendCursorUpdate method
        if (this.webrtc.sendCursorUpdate) {
            this.webrtc.sendCursorUpdate(x, y);
        } else {
            // Fallback to broadcast method
            const cursorData = {
                type: 'cursor-update',
                x: x,
                y: y,
                userId: this.webrtc.localUserId,
                userName: this.webrtc.localUserName,
                timestamp: Date.now()
            };

            this.webrtc.broadcast(cursorData);
        }
    }

    /**
     * Broadcast map change to all connected peers
     */
    broadcastMapChange(mapName) {
        const mapData = {
            type: 'map-change',
            mapName: mapName,
            userId: this.webrtc.localUserId,
            userName: this.webrtc.localUserName,
            timestamp: Date.now()
        };

        this.webrtc.broadcast(mapData);
        console.log('Broadcasted map change to:', mapName);
    }

    /**
     * Handle incoming collaboration data
     */
    handleIncomingData(data, fromPeerId) {
        try {
            switch (data.type) {
                case 'canvas-change':
                    this.handleCanvasChange(data);
                    break;

                case 'cursor-position':
                case 'cursor-update':
                    this.handleCursorPosition(data);
                    break;

                case 'map-change':
                    this.handleMapChange(data);
                    break;

                case 'user-join':
                    this.handleUserJoin(data, fromPeerId);
                    break;

                case 'user-leave':
                    this.handleUserLeave(data);
                    break;

                case 'canvas-sync-request':
                    this.handleSyncRequest(fromPeerId);
                    break;

                case 'canvas-sync-data':
                    this.handleSyncData(data);
                    break;

                default:
                    console.log('Unknown collaboration data type:', data.type);
            }
        } catch (error) {
            console.error('Error handling incoming data:', error);
        }
    }

    /**
     * Handle canvas changes from remote users
     */
    async handleCanvasChange(data) {
        try {
            const objectResult = this.deserializeObject(data.objectData);

            // Handle async image loading
            let object;
            if (objectResult instanceof Promise) {
                object = await objectResult;
            } else {
                object = objectResult;
            }

            if (!object) return;

            // Mark object as from remote to prevent re-broadcasting
            object.isFromRemote = true;

            switch (data.eventType) {
                case 'object:added':
                case 'path:created':
                    this.canvas.add(object);
                    break;

                case 'object:modified':
                    // Find and update existing object
                    const existingObject = this.findObjectById(object.id);
                    if (existingObject) {
                        existingObject.set(object);
                        existingObject.setCoords();
                    }
                    break;

                case 'object:removed':
                    const objectToRemove = this.findObjectById(object.id);
                    if (objectToRemove) {
                        this.canvas.remove(objectToRemove);
                    }
                    break;
            }

            this.canvas.renderAll();
        } catch (error) {
            console.error('Error handling canvas change:', error);
        }
    }

    /**
     * Handle cursor position updates from remote users
     */
    handleCursorPosition(data) {
        this.updateUserCursor(data.userId, data.userName, data.x, data.y);
    }

    /**
     * Handle map changes from remote users
     */
    handleMapChange(data) {
        // Check if this is a targeted sync message (for WebSocket compatibility)
        if (data.targetUserId && data.targetUserId !== this.webrtc.localUserId) {
            return; // This sync is not for us
        }

        console.log('Received map change from', data.userName, ':', data.mapName);

        // Update the map selector without triggering the change event
        const mapSelect = document.getElementById('map-select');
        if (mapSelect && mapSelect.value !== data.mapName) {
            // Temporarily remove the event listener to prevent infinite loop
            const originalHandler = mapSelect.onchange;
            mapSelect.onchange = null;

            // Update the dropdown value
            mapSelect.value = data.mapName;

            // Update the global current map variable
            if (window.currentMap !== undefined) {
                window.currentMap = data.mapName;
            }

            // Load the new map
            if (window.loadMap && typeof window.loadMap === 'function') {
                window.loadMap(data.mapName);
            }

            // Restore the event listener
            setTimeout(() => {
                mapSelect.onchange = originalHandler;
            }, 100);

            console.log('Map synchronized to:', data.mapName);
        }
    }

    /**
     * Handle user join events
     */
    handleUserJoin(data, fromPeerId) {
        console.log('User joined:', data.userName);
        this.addUser(data.userId, data.userName, fromPeerId);

        // If we're the host, send current canvas state and map to new user
        if (this.webrtc.isHost) {
            this.sendCanvasSync(fromPeerId);
            this.sendMapSync(fromPeerId);
        } else {
            // If we're not the host, request canvas sync
            this.requestCanvasSync();
        }
    }

    /**
     * Handle user leave events
     */
    handleUserLeave(data) {
        console.log('User left:', data.userName);
        this.removeUser(data.userId);
    }

    /**
     * Send current canvas state to a specific peer
     */
    sendCanvasSync(toPeerId) {
        try {
            const canvasData = JSON.stringify(this.canvas.toJSON());
            const syncData = {
                type: 'canvas-sync-data',
                canvasData: canvasData,
                timestamp: Date.now(),
                targetUserId: toPeerId // For WebSocket, we'll broadcast but include target
            };

            // For WebSocket, we broadcast the sync data
            // The server could implement targeted sending if needed
            this.webrtc.broadcast(syncData);
            console.log('Sent canvas sync to:', toPeerId);
        } catch (error) {
            console.error('Error sending canvas sync:', error);
        }
    }

    /**
     * Send current map state to a specific peer
     */
    sendMapSync(toPeerId) {
        try {
            const currentMap = window.currentMap || document.getElementById('map-select')?.value || 'Ascent';
            const mapSyncData = {
                type: 'map-change',
                mapName: currentMap,
                userId: this.webrtc.localUserId,
                userName: this.webrtc.localUserName,
                timestamp: Date.now(),
                isSync: true, // Flag to indicate this is a sync, not a user-initiated change
                targetUserId: toPeerId // For WebSocket, include target user
            };

            // For WebSocket, we broadcast the sync data
            this.webrtc.broadcast(mapSyncData);
            console.log('Sent map sync to:', toPeerId, 'Map:', currentMap);
        } catch (error) {
            console.error('Error sending map sync:', error);
        }
    }

    /**
     * Request canvas sync from host
     */
    requestCanvasSync() {
        const syncRequest = {
            type: 'canvas-sync-request',
            userId: this.webrtc.localUserId,
            timestamp: Date.now()
        };

        this.webrtc.broadcast(syncRequest);
        console.log('Requested canvas sync');
    }

    /**
     * Handle canvas sync requests (host only)
     */
    handleSyncRequest(fromPeerId) {
        if (this.webrtc.isHost) {
            this.sendCanvasSync(fromPeerId);
        }
    }

    /**
     * Handle incoming canvas sync data
     */
    handleSyncData(data) {
        try {
            // Check if this sync is targeted to us (for WebSocket compatibility)
            if (data.targetUserId && data.targetUserId !== this.webrtc.localUserId) {
                return; // This sync is not for us
            }

            this.canvas.isLoadingFromJSON = true;
            this.canvas.loadFromJSON(data.canvasData, () => {
                this.canvas.isLoadingFromJSON = false;
                this.canvas.renderAll();
                console.log('Canvas synced successfully');
            });
        } catch (error) {
            console.error('Error syncing canvas:', error);
            this.canvas.isLoadingFromJSON = false;
        }
    }

    /**
     * Serialize a Fabric.js object for transmission
     */
    serializeObject(object) {
        try {
            // Add unique ID if not present
            if (!object.id) {
                object.id = 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            const serialized = object.toObject(['id', 'isAgent', 'isDrawingPath', 'name']);

            // For agent images, include the source URL
            if (object.type === 'image' && object.isAgent && object._element) {
                serialized.src = object._element.src || object.getSrc();
            }

            return serialized;
        } catch (error) {
            console.error('Error serializing object:', error);
            return null;
        }
    }

    /**
     * Deserialize object data back to Fabric.js object
     */
    deserializeObject(objectData) {
        try {
            // Create object based on type
            let object;
            switch (objectData.type) {
                case 'image':
                    // Handle agent images specially
                    if (objectData.isAgent && objectData.src) {
                        return new Promise((resolve) => {
                            fabric.Image.fromURL(objectData.src, (img) => {
                                if (img) {
                                    img.set(objectData);
                                    img.isFromRemote = true;
                                    resolve(img);
                                } else {
                                    resolve(null);
                                }
                            }, { crossOrigin: 'Anonymous' });
                        });
                    }
                    return null;

                case 'path':
                    object = new fabric.Path(objectData.path, objectData);
                    break;

                case 'line':
                    object = new fabric.Line([objectData.x1, objectData.y1, objectData.x2, objectData.y2], objectData);
                    break;

                case 'rect':
                    object = new fabric.Rect(objectData);
                    break;

                case 'circle':
                    object = new fabric.Circle(objectData);
                    break;

                case 'triangle':
                    object = new fabric.Triangle(objectData);
                    break;

                case 'group':
                    // Handle groups recursively
                    const objects = objectData.objects.map(obj => this.deserializeObject(obj)).filter(Boolean);
                    object = new fabric.Group(objects, objectData);
                    break;

                default:
                    console.warn('Unknown object type:', objectData.type);
                    return null;
            }

            return object;
        } catch (error) {
            console.error('Error deserializing object:', error);
            return null;
        }
    }

    /**
     * Find object by ID
     */
    findObjectById(id) {
        return this.canvas.getObjects().find(obj => obj.id === id);
    }

    /**
     * Add a connected user
     */
    addUser(userId, userName, peerId) {
        this.connectedUsers.set(userId, {
            userName: userName,
            peerId: peerId,
            joinTime: Date.now()
        });
        this.updateUsersDisplay();
    }

    /**
     * Remove a connected user
     */
    removeUser(userId) {
        this.connectedUsers.delete(userId);
        this.removeUserCursor(userId);
        this.updateUsersDisplay();
    }

    /**
     * Update user cursor position
     */
    updateUserCursor(userId, userName, x, y) {
        // Implementation for showing user cursors would go here
        // This is a placeholder for the cursor visualization
        console.log(`User ${userName} cursor at (${x}, ${y})`);
    }

    /**
     * Remove user cursor
     */
    removeUserCursor(userId) {
        this.userCursors.delete(userId);
    }

    /**
     * Update the users display panel
     */
    updateUsersDisplay() {
        const usersContainer = document.getElementById('connected-users');
        if (!usersContainer) return;

        usersContainer.innerHTML = '';

        // Add local user
        const localUserDiv = document.createElement('div');
        localUserDiv.className = 'user-item local-user';
        localUserDiv.innerHTML = `
            <span class="user-indicator"></span>
            <span class="user-name">${this.webrtc.localUserName} (You)</span>
        `;
        usersContainer.appendChild(localUserDiv);

        // Add connected users
        this.connectedUsers.forEach((user, userId) => {
            const userDiv = document.createElement('div');
            userDiv.className = 'user-item';
            userDiv.innerHTML = `
                <span class="user-indicator"></span>
                <span class="user-name">${user.userName}</span>
            `;
            usersContainer.appendChild(userDiv);
        });
    }

    /**
     * Show the users panel
     */
    showUsersPanel() {
        const usersPanel = document.getElementById('users-panel');
        if (usersPanel) {
            usersPanel.style.display = 'block';
            this.updateUsersDisplay();
        }
    }

    /**
     * Hide the users panel
     */
    hideUsersPanel() {
        const usersPanel = document.getElementById('users-panel');
        if (usersPanel) {
            usersPanel.style.display = 'none';
        }
    }

    /**
     * Update connection status display
     */
    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        const statusText = document.querySelector('#connection-status .status-text');

        if (statusElement && statusText) {
            statusElement.className = `connection-status ${status}`;

            switch (status) {
                case 'connected':
                    statusText.textContent = `Connected (${this.connectedUsers.size + 1} users)`;
                    break;
                case 'connecting':
                    statusText.textContent = 'Connecting...';
                    break;
                case 'offline':
                    statusText.textContent = 'Offline';
                    break;
                case 'error':
                    statusText.textContent = 'Connection Error';
                    break;
                default:
                    statusText.textContent = 'Unknown';
            }
        }
    }

    /**
     * Disconnect from collaboration
     */
    disconnect() {
        this.isCollaborating = false;
        this.connectedUsers.clear();
        this.userCursors.clear();
        this.webrtc.disconnect();
        this.updateConnectionStatus('offline');
        this.hideUsersPanel();
        console.log('Disconnected from collaboration');
    }
}

// Export for use in other files
window.CollaborationManager = CollaborationManager;
