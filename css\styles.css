/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #0F1923;
    color: #ECE8E1;
    line-height: 1.6;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1600px;
    margin: 0 auto;
    padding: 10px;
}

/* Header Styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #1F2731;
    border-radius: 5px;
    margin-bottom: 10px;
}

header h1 {
    color: #FF4655;
    font-size: 1.5rem;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.map-selector, .side-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Collaboration Controls */
.collaboration-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.collab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.collab-btn.primary {
    background-color: #FF4655;
    border-color: #FF4655;
    color: white;
}

.collab-btn.primary:hover {
    background-color: #E63946;
    transform: translateY(-1px);
}

.collab-btn.hosting {
    background-color: #28A745;
    border-color: #28A745;
    color: white;
}

.collab-btn.hosting:hover {
    background-color: #218838;
}

.collab-btn.connected {
    background-color: #17A2B8;
    border-color: #17A2B8;
    color: white;
}

.collab-btn.connected:hover {
    background-color: #138496;
}

/* Session Info Display */
.session-info-display {
    display: flex;
    align-items: center;
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: 6px;
    padding: 6px 10px;
}

.session-code-display-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.session-label {
    font-size: 0.85rem;
    color: #28A745;
    font-weight: 500;
}

.session-code-text {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    font-weight: bold;
    color: #ECE8E1;
    background-color: rgba(40, 167, 69, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    letter-spacing: 1px;
}

.copy-code-btn {
    background: none;
    border: none;
    color: #28A745;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.8rem;
    transition: background-color 0.2s ease;
}

.copy-code-btn:hover {
    background-color: rgba(40, 167, 69, 0.2);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.connection-status.offline {
    background-color: rgba(108, 117, 125, 0.2);
    color: #6C757D;
}

.connection-status.connecting {
    background-color: rgba(255, 193, 7, 0.2);
    color: #FFC107;
}

.connection-status.connected {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28A745;
}

.connection-status.error {
    background-color: rgba(220, 53, 69, 0.2);
    color: #DC3545;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

select, button, input[type="text"] {
    background-color: #364966;
    color: #ECE8E1;
    border: 1px solid #535F70;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 0.9rem;
}

select:hover, button:hover {
    background-color: #465A7D;
}

/* Main Content Styles */
main {
    display: flex;
    flex: 1;
    gap: 10px;
    overflow: hidden;
}

.canvas-container {
    flex: 1;
    background-color: #1F2731;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    cursor: grab;
    z-index: 1;
}

.canvas-container:active {
    cursor: grabbing !important;
}

/* Canvas wrapper to properly handle zooming */
.canvas-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    touch-action: none; /* Prevents browser handling of touch gestures */
}

/* Make sure the canvas scales properly */
canvas {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: block !important;
}

/* Ensure proper layering of canvas elements - FIXED Z-INDEX ISSUES */
.lower-canvas {
    z-index: 10 !important;
    position: absolute !important;
}

.upper-canvas {
    z-index: 20 !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    pointer-events: auto !important;
    cursor: crosshair !important;
}

/* Force fabric.js to properly maintain canvas dimensions */
.canvas-container canvas {
    visibility: visible !important;
    opacity: 1 !important;
    max-width: none !important;
    max-height: none !important;
}

/* Ensure drawing tools and crosshair appear above canvas */
.canvas-container {
    position: relative;
    z-index: 1;
}

/* Fix crosshair cursor for drawing tools */
.canvas-container.drawing-mode {
    cursor: crosshair !important;
}

.canvas-container.drawing-mode .upper-canvas {
    cursor: crosshair !important;
}

/* Ensure eraser indicator appears above everything */
.canvas-container .fabric-object {
    z-index: 15 !important;
}

/* Prevent text selection during drag operations */
.canvas-container * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Add a hint about zoom/pan functionality */
.canvas-container::after {
    content: "Scroll to zoom • Alt+click or middle click to pan";
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 5px 10px;
    font-size: 0.8rem;
    border-radius: 3px;
    pointer-events: none;
    opacity: 0.7;
    z-index: 100;
}

.sidebar {
    width: 150px;
    background-color: #1F2731;
    border-radius: 5px;
    padding: 10px;
    overflow-y: auto;
}

.agents-panel h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    text-align: center;
}

.agent-icons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    justify-items: center;
}

.agent-icon {
    width: 45px;
    height: 45px;
    cursor: grab;
    transition: transform 0.2s;
}

.agent-icon:hover {
    transform: scale(1.1);
}

/* Users Panel Styles */
.users-panel {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #364966;
}

.users-panel h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    text-align: center;
    color: #28A745;
}

.connected-users {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.user-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    background-color: rgba(54, 73, 102, 0.3);
    border-radius: 4px;
    font-size: 0.85rem;
}

.user-item.local-user {
    background-color: rgba(255, 70, 85, 0.2);
    border: 1px solid rgba(255, 70, 85, 0.3);
}

.user-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #28A745;
    flex-shrink: 0;
}

.user-name {
    font-weight: 500;
    color: #ECE8E1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Footer Styles */
footer {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #1F2731;
    border-radius: 5px;
    margin-top: 10px;
}

.drawing-tools {
    display: flex;
    gap: 5px;
    align-items: center;
}

.tool-btn {
    width: 40px;
    height: 40px;
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
}

.tool-btn.active {
    background-color: #FF4655;
    border-color: #FF4655;
}

/* Zoom Controls */
.zoom-controls {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: 15px;
}

.zoom-controls button {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    font-weight: bold;
    padding: 0;
    border-radius: 3px;
    cursor: pointer;
}

#zoom-reset {
    width: auto;
    font-size: 0.8rem;
    padding: 0 8px;
}

.tooltip {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #000;
    color: #fff;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.tool-btn:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

.color-picker, .brush-size {
    display: flex;
    align-items: center;
    gap: 5px;
}

input[type="color"] {
    width: 30px;
    height: 30px;
    border: none;
    background: none;
    cursor: pointer;
}

input[type="range"] {
    width: 80px;
}

.strategy-controls {
    display: flex;
    gap: 5px;
    align-items: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
    background-color: #1F2731;
    margin: 10% auto;
    padding: 20px;
    border-radius: 5px;
    width: 60%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
}

#saved-strategies-list {
    margin-top: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.strategy-item {
    padding: 10px;
    border-bottom: 1px solid #364966;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
}

.strategy-item:hover {
    background-color: #364966;
}

.delete-strategy {
    color: #FF4655;
    cursor: pointer;
}

/* Collaboration Modal Styles */
.collab-mode-selector {
    text-align: center;
    margin-bottom: 20px;
}

.collab-mode-selector p {
    margin-bottom: 20px;
    color: #ECE8E1;
    font-size: 1rem;
}

.collab-mode-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.collab-mode-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: #364966;
    border: 2px solid #535F70;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    text-align: center;
}

.collab-mode-btn:hover {
    background-color: #465A7D;
    border-color: #FF4655;
    transform: translateY(-2px);
}

.collab-mode-btn.primary {
    border-color: #FF4655;
}

.collab-mode-btn .icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.collab-mode-btn .btn-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ECE8E1;
    margin-bottom: 5px;
    display: block;
}

.collab-mode-btn .btn-desc {
    font-size: 0.85rem;
    color: #B0B8C1;
    display: block;
}

.collab-panel {
    margin-top: 20px;
}

.collab-panel h3 {
    color: #FF4655;
    margin-bottom: 15px;
    text-align: center;
}

.session-info {
    margin-bottom: 15px;
}

.session-info label {
    display: block;
    margin-bottom: 5px;
    color: #ECE8E1;
    font-weight: 500;
}

.session-info input {
    width: 100%;
    padding: 8px 12px;
    background-color: #364966;
    border: 1px solid #535F70;
    border-radius: 4px;
    color: #ECE8E1;
    font-size: 1rem;
}

.session-code-display {
    margin-bottom: 15px;
}

.code-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.code-container input {
    flex: 1;
    padding: 10px 12px;
    background-color: #1F2731;
    border: 2px solid #FF4655;
    border-radius: 4px;
    color: #ECE8E1;
    font-size: 1.2rem;
    font-weight: bold;
    text-align: center;
    letter-spacing: 2px;
}

.copy-btn {
    padding: 10px 15px;
    background-color: #28A745;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.copy-btn:hover {
    background-color: #218838;
}

.status-message {
    padding: 10px;
    background-color: rgba(54, 73, 102, 0.3);
    border-radius: 4px;
    color: #ECE8E1;
    text-align: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.panel-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background-color: #FF4655;
    color: white;
}

.action-btn.primary:hover {
    background-color: #E63946;
}

.action-btn.secondary {
    background-color: #6C757D;
    color: white;
}

.action-btn.secondary:hover {
    background-color: #5A6268;
}

/* Mobile Warning */
.mobile-warning {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 25, 35, 0.95);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: none;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.mobile-warning.show {
    display: flex;
}

.mobile-warning-content {
    background-color: #1F2731;
    border-radius: 12px;
    padding: 30px;
    max-width: 400px;
    text-align: center;
    border: 2px solid #FF4655;
}

.mobile-warning-content h2 {
    color: #FF4655;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.mobile-warning-content p {
    color: #ECE8E1;
    margin-bottom: 15px;
    line-height: 1.5;
}

.mobile-warning-content ul {
    color: #ECE8E1;
    text-align: left;
    margin: 15px 0;
    padding-left: 20px;
}

.mobile-warning-content li {
    margin-bottom: 5px;
}

.continue-btn {
    background-color: #FF4655;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 15px;
}

.continue-btn:hover {
    background-color: #E63946;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-controls {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .collaboration-controls {
        flex-wrap: wrap;
        gap: 10px;
    }
}

@media (max-width: 900px) {
    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .header-controls {
        width: 100%;
        flex-direction: column;
        align-items: stretch;
    }

    .collaboration-controls {
        justify-content: space-between;
        width: 100%;
    }

    footer {
        flex-direction: column;
        gap: 15px;
    }

    .drawing-tools, .strategy-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .canvas-container {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 5px;
    }

    main {
        flex-direction: column;
        gap: 10px;
    }

    .sidebar {
        width: 100%;
        height: auto;
        max-height: 150px;
        overflow-y: auto;
    }

    .agent-icons {
        grid-template-columns: repeat(6, 1fr);
        gap: 5px;
    }

    .agent-icon {
        width: 35px;
        height: 35px;
    }

    .canvas-container {
        height: 300px;
        order: -1;
    }

    .tool-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .drawing-tools {
        gap: 3px;
    }

    .strategy-controls {
        gap: 5px;
    }

    .strategy-controls input[type="text"] {
        max-width: 120px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.2rem;
    }

    .collaboration-controls {
        flex-direction: column;
        gap: 8px;
    }

    .session-info-display {
        width: 100%;
        justify-content: center;
    }

    .canvas-container {
        height: 250px;
    }

    .agent-icons {
        grid-template-columns: repeat(8, 1fr);
    }

    .agent-icon {
        width: 30px;
        height: 30px;
    }

    .drawing-tools {
        justify-content: space-around;
    }

    .tool-btn {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
}

/* Keyboard Shortcut Notification */
.shortcut-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.shortcut-notification.visible {
    opacity: 1;
}

/* Creator Footer Styles */
.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-top: 2px solid #ff6b6b;
    padding: 15px 20px;
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    flex-wrap: wrap;
    gap: 15px;
}

.creator-info {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.created-by {
    color: #ffffff;
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

.youtube-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ff4757;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    padding: 8px 15px;
    border: 2px solid #ff4757;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255, 71, 87, 0.1);
}

.youtube-link:hover {
    background: #ff4757;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
}

.youtube-icon {
    font-size: 16px;
}

.support-section {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.support-text {
    color: #ffffff;
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

/* Eye-catching Coffee Button */
.coffee-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(45deg, #ff6b6b, #ffa500, #ff6b6b);
    background-size: 200% 200%;
    color: white;
    text-decoration: none;
    font-weight: bold;
    font-size: 15px;
    padding: 12px 20px;
    border-radius: 30px;
    border: 3px solid #fff;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    transition: all 0.3s ease;
    animation: gradient-shift 3s ease infinite, pulse-glow 2s ease-in-out infinite alternate;
    position: relative;
    overflow: hidden;
}

.coffee-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.6);
    animation: gradient-shift 1s ease infinite, pulse-glow 0.5s ease-in-out infinite alternate;
}

.coffee-button:active {
    transform: translateY(-1px) scale(1.02);
}

.coffee-icon {
    font-size: 18px;
    animation: bounce 2s ease-in-out infinite;
}

.coffee-text {
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.coffee-sparkle {
    font-size: 14px;
    animation: sparkle 1.5s ease-in-out infinite;
}

/* Animations */
@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes pulse-glow {
    0% { box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4); }
    100% { box-shadow: 0 6px 25px rgba(255, 107, 107, 0.7), 0 0 30px rgba(255, 165, 0, 0.3); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-4px); }
    60% { transform: translateY(-2px); }
}

@keyframes sparkle {
    0%, 100% { transform: rotate(0deg) scale(1); opacity: 1; }
    25% { transform: rotate(90deg) scale(1.2); opacity: 0.8; }
    50% { transform: rotate(180deg) scale(1); opacity: 1; }
    75% { transform: rotate(270deg) scale(1.2); opacity: 0.8; }
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .creator-info {
        flex-direction: column;
        gap: 10px;
    }

    .support-section {
        flex-direction: column;
        gap: 10px;
    }

    .coffee-button {
        font-size: 14px;
        padding: 10px 18px;
    }

    .youtube-link {
        font-size: 13px;
        padding: 6px 12px;
    }
}

/* Adjust main content to account for footer */
body {
    padding-bottom: 80px; /* Space for footer */
}